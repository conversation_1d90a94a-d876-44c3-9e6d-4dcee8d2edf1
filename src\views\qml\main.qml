import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls.Material 2.15
import EmailManager 1.0
import "pages"
import "components"

ApplicationWindow {
    id: window
    width: 1280
    height: 800
    minimumWidth: 1024
    minimumHeight: 768
    visible: true
    title: appName || "域名邮箱管理器"

    // 🔧 根本解决方案 - 强制稳定背景，禁用重新渲染
    color: "#FAFAFA"  // 设置窗口背景色，避免透明导致的白色闪烁

    // 强制禁用窗口的自动重新渲染
    property bool __disableAutoRender: true

    // 🔧 终极解决方案 - 完全控制窗口渲染行为
    flags: Qt.Window | Qt.FramelessWindowHint | Qt.WindowSystemMenuHint | Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint

    // Material Design主题
    Material.theme: Material.Light
    Material.primary: Material.Blue
    Material.accent: Material.Cyan

    // 🔧 Windows DWM闪烁问题解决方案将在Component.onCompleted中处理

    // 🔧 根本解决方案 - 完全禁用焦点变化响应
    property bool renderingStable: false

    // 完全移除onActiveChanged处理器，避免焦点变化触发重新渲染
    // onActiveChanged: {
    //     // 不再响应焦点变化，彻底避免重新渲染
    // }

    // 响应式布局检测
    readonly property string screenSize: {
        if (width < 480) return "xs"
        if (width < 768) return "sm"
        if (width < 1024) return "md"
        if (width < 1440) return "lg"
        return "xl"
    }

    readonly property bool isMobile: screenSize === "xs" || screenSize === "sm"
    readonly property bool isTablet: screenSize === "md"
    readonly property bool isDesktop: screenSize === "lg" || screenSize === "xl"

    // 🔧 步骤2：恢复基本的应用程序状态
    property bool isConfigured: configController ? configController.isConfigured() : false
    property string currentDomain: emailController ? emailController.getCurrentDomain() : "未配置"
    property var statistics: emailController ? emailController.getStatistics() : ({})

    // 🔧 禁用键盘快捷键支持 - 可能导致重新渲染
    // Item {
    //     anchors.fill: parent
    //     focus: true
    //     z: -1
    //     // 键盘快捷键被禁用
    // }

    // 应用程序状态（已在上面定义，移除重复）

    // 全局状态管理
    property var globalState: ({
        emailList: [],
        tagList: [],
        currentPage: 1,
        totalPages: 1,
        isLoading: false
    })

    // 🔧 最小化初始化 - 只保留基本功能
    Component.onCompleted: {
        console.log("🔧 最小化测试版本启动")

        // 只设置基本的窗口属性
        if (Qt.platform.os === "windows") {
            console.log("🔧 [Windows] 最小化闪烁测试")
            window.color = "#FAFAFA"
            window.renderingStable = true
        }

        console.log("🔧 最小化测试版本初始化完成")
    }

    // 🔧 禁用所有复杂函数
    // function handlePageSwitch(pageIndex) {
    //     // 页面切换处理被禁用
    // }

    // function initializeGlobalState() {
    //     // 全局状态初始化被禁用
    // }

    // function refreshTagList() {
    //     // 标签列表刷新被禁用
    // }

    // 🔧 禁用所有页面管理函数
    // function refreshCurrentPage() {
    //     // 页面刷新被禁用
    // }

    // function navigateToPage(pageIndex, showMessage) {
    //     // 页面导航被禁用
    // }

    // function checkPageAccess(pageIndex) {
    //     // 页面访问检查被禁用
    // }

    // 🔧 禁用所有错误处理函数
    // function handleError(errorType, errorMessage, context) {
    //     // 错误处理被禁用
    // }

    // function getUserFriendlyErrorMessage(errorType, errorMessage) {
    //     // 错误消息处理被禁用
    // }

    // function performErrorRecovery(errorType, context) {
    //     // 错误恢复被禁用
    // }

    // 🔧 禁用所有性能监控和定时器
    // property var performanceMetrics: ({
    //     // 性能监控被禁用
    // })

    // function recordPerformanceMetric(category, operation, startTime) {
    //     // 性能监控被禁用
    // }

    // function performMemoryCleanup() {
    //     // 内存清理被禁用
    // }

    // Timer {
    //     // 定期内存清理定时器被禁用
    // }

    // 🔧 移除焦点稳定化定时器 - 不再需要焦点管理
    // Timer {
    //     id: focusStabilizer
    //     interval: 500 // 500ms检查一次
    //     running: window.focusStabilized
    //     repeat: true
    //
    //     onTriggered: {
    //         // 如果窗口意外失去焦点，重新激活
    //         if (window.focusStabilized && !window.active) {
    //             console.log("🔧 [定时器] 检测到焦点丢失，重新激活窗口")
    //             window.raise()
    //             window.requestActivate()
    //         }
    //     }
    // }
    
    // 🔧 禁用移动设备抽屉导航
    // Drawer {
    //     id: mobileDrawer
    //     // 移动设备抽屉被禁用
    // }

    // 🔧 步骤1：添加基本布局结构
    Rectangle {
        anchors.fill: parent
        color: "#FAFAFA"  // 背景色
        z: -1000
    }

    // 基本的列布局
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 0
        spacing: 0

        // 🔧 步骤2：添加真正的AppToolBar
        AppToolBar {
            id: appToolBar
            Layout.fillWidth: true
            title: window.title
            isConfigured: window.isConfigured
            currentDomain: window.currentDomain

            // 移动设备支持
            showMenuButton: window.isMobile

            onMenuClicked: {
                // mobileDrawer.open() // 暂时注释掉
            }

            onConfigStatusClicked: {
                // 切换到配置页面 - 暂时注释掉
                // tabBar.currentIndex = 3
                console.log("配置状态点击")
            }
        }

        // 🔧 步骤2：添加TabBar
        TabBar {
            id: tabBar
            Layout.fillWidth: true
            Material.background: "#FAFAFA"

            // 响应式布局调整
            visible: !window.isMobile

            onCurrentIndexChanged: {
                console.log("🔧 TabBar切换到索引:", currentIndex)
            }

            TabButton {
                text: "🏠 邮箱生成"
                font.pixelSize: 14
                width: implicitWidth

                // 未配置时的提示
                ToolTip.visible: !window.isConfigured && hovered
                ToolTip.text: "请先完成域名配置"
            }
            TabButton {
                text: "📋 邮箱管理"
                font.pixelSize: 14
                width: implicitWidth
            }
            TabButton {
                text: "🏷️ 标签管理"
                font.pixelSize: 14
                width: implicitWidth
            }
            TabButton {
                text: "⚙️ 配置管理"
                font.pixelSize: 14
                width: implicitWidth
            }
        }

        // 🔧 步骤3：添加StackLayout和简单页面内容
        StackLayout {
            id: stackLayout
            Layout.fillWidth: true
            Layout.fillHeight: true
            currentIndex: tabBar.currentIndex

            // 邮箱生成页面 - 简化版本
            Rectangle {
                color: "#FAFAFA"

                Text {
                    anchors.centerIn: parent
                    text: "📧 邮箱生成页面\n步骤3：测试StackLayout"
                    font.pixelSize: 20
                    color: "#333333"
                    horizontalAlignment: Text.AlignHCenter
                }
            }

            // 邮箱管理页面 - 简化版本
            Rectangle {
                color: "#F5F5F5"

                Text {
                    anchors.centerIn: parent
                    text: "📋 邮箱管理页面\n步骤3：测试页面切换"
                    font.pixelSize: 20
                    color: "#333333"
                    horizontalAlignment: Text.AlignHCenter
                }
            }

            // 标签管理页面 - 简化版本
            Rectangle {
                color: "#E8F5E8"

                Text {
                    anchors.centerIn: parent
                    text: "🏷️ 标签管理页面\n步骤3：测试不同背景色"
                    font.pixelSize: 20
                    color: "#333333"
                    horizontalAlignment: Text.AlignHCenter
                }
            }

            // 配置管理页面 - 简化版本
            Rectangle {
                color: "#FFF3E0"

                Text {
                    anchors.centerIn: parent
                    text: "⚙️ 配置管理页面\n步骤3：测试页面内容"
                    font.pixelSize: 20
                    color: "#333333"
                    horizontalAlignment: Text.AlignHCenter
                }
            }
        }

        // 底部占位区域
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            color: "#F5F5F5"

            Text {
                anchors.centerIn: parent
                text: "底部状态区域"
                font.pixelSize: 12
                color: "#999999"
            }
        }
    }

    // 🔧 禁用所有可能导致闪烁的组件
    // TextArea {
    //     id: mainLogArea
    //     visible: false
    //     function addLog(message) {
    //         var timestamp = new Date().toLocaleTimeString()
    //         text += "\n[" + timestamp + "] " + message
    //     }
    // }

    // 禁用状态栏和定时器
    // Rectangle {
    //     anchors.bottom: parent.bottom
    //     width: parent.width
    //     height: 32
    //     color: "#f8f9fa"
    //     border.color: "#e9ecef"
    //     // 所有状态栏内容都被禁用
    // }

    // 🔧 禁用所有调试面板和连接器
    // DebugPanel {
    //     // 调试面板被禁用
    // }

    // 禁用所有Connections
    // Connections {
    //     target: emailController
    //     // 所有邮箱控制器连接被禁用
    // }

    // Connections {
    //     target: configController
    //     // 所有配置控制器连接被禁用
    // }

    // ==================== 性能监控和测试工具 ====================

    // 性能监控器 (暂时禁用)
    // PerformanceMonitor {
    //     id: performanceMonitor
    //     anchors.fill: parent
    //     enabled: false
    //     showOverlay: false
    // }

    // UX测试套件 (暂时禁用)
    // UXTestSuite {
    //     id: uxTestSuite
    //     anchors.fill: parent
    //     testingEnabled: false

    //     onTestCompleted: function(testName, results) {
    //         if (!results.passed) {
    //             globalStatusMessage.showError("测试失败: " + testName)
    //         }
    //     }

    //     onAllTestsCompleted: function(summary) {
    //         globalStatusMessage.showSuccess(
    //             "测试完成: " + summary.passedTests + "/" + summary.totalTests +
    //             " 通过 (" + summary.successRate + ")"
    //         )
    //     }
    // }

    // ==================== 初始化和优化 ====================
}